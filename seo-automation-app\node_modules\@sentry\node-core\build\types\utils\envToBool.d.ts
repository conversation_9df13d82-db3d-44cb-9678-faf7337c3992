export declare const FALSY_ENV_VALUES: Set<string>;
export declare const TRUTHY_ENV_VALUES: Set<string>;
export type StrictBoolCast = {
    strict: true;
};
export type LooseBoolCast = {
    strict?: false;
};
export type BoolCastOptions = StrictBoolCast | LooseBoolCast;
export declare function envToBool(value: unknown, options?: LooseBoolCast): boolean;
export declare function envToBool(value: unknown, options: StrictBoolCast): boolean | null;
export declare function envToBool(value: unknown, options?: BoolCastOptions): boolean | null;
//# sourceMappingURL=envToBool.d.ts.map