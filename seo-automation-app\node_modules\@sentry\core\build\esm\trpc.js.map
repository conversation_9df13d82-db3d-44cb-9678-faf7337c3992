{"version": 3, "file": "trpc.js", "sources": ["../../src/trpc.ts"], "sourcesContent": ["import { getClient, withI<PERSON>S<PERSON> } from './currentScopes';\nimport { captureException } from './exports';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE } from './semanticAttributes';\nimport { startSpanManual } from './tracing';\nimport { normalize } from './utils/normalize';\nimport { addNonEnumerableProperty } from './utils/object';\n\ninterface SentryTrpcMiddlewareOptions {\n  /** Whether to include procedure inputs in reported events. Defaults to `false`. */\n  attachRpcInput?: boolean;\n  forceTransaction?: boolean;\n}\n\nexport interface SentryTrpcMiddlewareArguments<T> {\n  path?: unknown;\n  type?: unknown;\n  next: () => T;\n  rawInput?: unknown;\n  getRawInput?: () => Promise<unknown>;\n}\n\nconst trpcCaptureContext = { mechanism: { handled: false, data: { function: 'trpcMiddleware' } } };\n\nfunction captureIfError(nextResult: unknown): void {\n  // TODO: Set span status based on what TRPCError was encountered\n  if (\n    typeof nextResult === 'object' &&\n    nextResult !== null &&\n    'ok' in nextResult &&\n    !nextResult.ok &&\n    'error' in nextResult\n  ) {\n    captureException(nextResult.error, trpcCaptureContext);\n  }\n}\n\ntype SentryTrpcMiddleware<T> = T extends Promise<unknown> ? T : Promise<T>;\n\n/**\n * Sentry tRPC middleware that captures errors and creates spans for tRPC procedures.\n */\nexport function trpcMiddleware(options: SentryTrpcMiddlewareOptions = {}) {\n  // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n  // @ts-ignore\n  return async function <T>(opts: SentryTrpcMiddlewareArguments<T>): SentryTrpcMiddleware<T> {\n    const { path, type, next, rawInput, getRawInput } = opts;\n\n    const client = getClient();\n    const clientOptions = client?.getOptions();\n\n    const trpcContext: Record<string, unknown> = {\n      procedure_path: path,\n      procedure_type: type,\n    };\n\n    addNonEnumerableProperty(\n      trpcContext,\n      '__sentry_override_normalization_depth__',\n      1 + // 1 for context.input + the normal normalization depth\n        (clientOptions?.normalizeDepth ?? 5), // 5 is a sane depth\n    );\n\n    if (options.attachRpcInput !== undefined ? options.attachRpcInput : clientOptions?.sendDefaultPii) {\n      if (rawInput !== undefined) {\n        trpcContext.input = normalize(rawInput);\n      }\n\n      if (getRawInput !== undefined && typeof getRawInput === 'function') {\n        try {\n          const rawRes = await getRawInput();\n\n          trpcContext.input = normalize(rawRes);\n        } catch (err) {\n          // noop\n        }\n      }\n    }\n\n    return withIsolationScope(scope => {\n      scope.setContext('trpc', trpcContext);\n      return startSpanManual(\n        {\n          name: `trpc/${path}`,\n          op: 'rpc.server',\n          attributes: {\n            [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'route',\n            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.rpc.trpc',\n          },\n          forceTransaction: !!options.forceTransaction,\n        },\n        async span => {\n          try {\n            const nextResult = await next();\n            captureIfError(nextResult);\n            span.end();\n            return nextResult;\n          } catch (e) {\n            captureException(e, trpcCaptureContext);\n            span.end();\n            throw e;\n          }\n        },\n      ) as SentryTrpcMiddleware<T>;\n    });\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAqBA,MAAM,qBAAqB,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,gBAAA,EAAiB,IAAK;;AAElG,SAAS,cAAc,CAAC,UAAU,EAAiB;AACnD;AACA,EAAE;AACF,IAAI,OAAO,UAAA,KAAe,QAAA;AAC1B,IAAI,UAAA,KAAe,IAAA;AACnB,IAAI,IAAA,IAAQ,UAAA;AACZ,IAAI,CAAC,UAAU,CAAC,EAAA;AAChB,IAAI,WAAW;AACf,IAAI;AACJ,IAAI,gBAAgB,CAAC,UAAU,CAAC,KAAK,EAAE,kBAAkB,CAAC;AAC1D;AACA;;AAIA;AACA;AACA;AACO,SAAS,cAAc,CAAC,OAAO,GAAgC,EAAE,EAAE;AAC1E;AACA;AACA,EAAE,OAAO,gBAAmB,IAAI,EAA6D;AAC7F,IAAI,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAA,EAAY,GAAI,IAAI;;AAE5D,IAAI,MAAM,MAAA,GAAS,SAAS,EAAE;AAC9B,IAAI,MAAM,aAAA,GAAgB,MAAM,EAAE,UAAU,EAAE;;AAE9C,IAAI,MAAM,WAAW,GAA4B;AACjD,MAAM,cAAc,EAAE,IAAI;AAC1B,MAAM,cAAc,EAAE,IAAI;AAC1B,KAAK;;AAEL,IAAI,wBAAwB;AAC5B,MAAM,WAAW;AACjB,MAAM,yCAAyC;AAC/C,MAAM,CAAA;AACN,SAAS,aAAa,EAAE,cAAA,IAAkB,CAAC,CAAC;AAC5C,KAAK;;AAEL,IAAI,IAAI,OAAO,CAAC,mBAAmB,SAAA,GAAY,OAAO,CAAC,cAAA,GAAiB,aAAa,EAAE,cAAc,EAAE;AACvG,MAAM,IAAI,QAAA,KAAa,SAAS,EAAE;AAClC,QAAQ,WAAW,CAAC,KAAA,GAAQ,SAAS,CAAC,QAAQ,CAAC;AAC/C;;AAEA,MAAM,IAAI,WAAA,KAAgB,SAAA,IAAa,OAAO,WAAA,KAAgB,UAAU,EAAE;AAC1E,QAAQ,IAAI;AACZ,UAAU,MAAM,MAAA,GAAS,MAAM,WAAW,EAAE;;AAE5C,UAAU,WAAW,CAAC,KAAA,GAAQ,SAAS,CAAC,MAAM,CAAC;AAC/C,SAAQ,CAAE,OAAO,GAAG,EAAE;AACtB;AACA;AACA;AACA;;AAEA,IAAI,OAAO,kBAAkB,CAAC,KAAA,IAAS;AACvC,MAAM,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,WAAW,CAAC;AAC3C,MAAM,OAAO,eAAe;AAC5B,QAAQ;AACR,UAAU,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AACA,UAAA,EAAA,EAAA,YAAA;AACA,UAAA,UAAA,EAAA;AACA,YAAA,CAAA,gCAAA,GAAA,OAAA;AACA,YAAA,CAAA,gCAAA,GAAA,eAAA;AACA,WAAA;AACA,UAAA,gBAAA,EAAA,CAAA,CAAA,OAAA,CAAA,gBAAA;AACA,SAAA;AACA,QAAA,MAAA,IAAA,IAAA;AACA,UAAA,IAAA;AACA,YAAA,MAAA,UAAA,GAAA,MAAA,IAAA,EAAA;AACA,YAAA,cAAA,CAAA,UAAA,CAAA;AACA,YAAA,IAAA,CAAA,GAAA,EAAA;AACA,YAAA,OAAA,UAAA;AACA,WAAA,CAAA,OAAA,CAAA,EAAA;AACA,YAAA,gBAAA,CAAA,CAAA,EAAA,kBAAA,CAAA;AACA,YAAA,IAAA,CAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA;AACA;AACA,SAAA;AACA,OAAA;AACA,KAAA,CAAA;AACA,GAAA;AACA;;;;"}