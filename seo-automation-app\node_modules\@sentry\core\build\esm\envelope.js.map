{"version": 3, "file": "envelope.js", "sources": ["../../src/envelope.ts"], "sourcesContent": ["import type { Client } from './client';\nimport { getDynamicSamplingContextFromSpan } from './tracing/dynamicSamplingContext';\nimport type { SentrySpan } from './tracing/sentrySpan';\nimport type { LegacyCSPReport } from './types-hoist/csp';\nimport type { DsnComponents } from './types-hoist/dsn';\nimport type {\n  DynamicSamplingContext,\n  EventEnvelope,\n  EventItem,\n  RawSecurityEnvelope,\n  RawSecurityItem,\n  SessionEnvelope,\n  SessionItem,\n  SpanEnvelope,\n  SpanItem,\n} from './types-hoist/envelope';\nimport type { Event } from './types-hoist/event';\nimport type { SdkInfo } from './types-hoist/sdkinfo';\nimport type { SdkMetadata } from './types-hoist/sdkmetadata';\nimport type { Session, SessionAggregates } from './types-hoist/session';\nimport { dsnToString } from './utils/dsn';\nimport {\n  createEnvelope,\n  createEventEnvelopeHeaders,\n  createSpanEnvelopeItem,\n  getSdkMetadataForEnvelopeHeader,\n} from './utils/envelope';\nimport { uuid4 } from './utils/misc';\nimport { showSpanDropWarning, spanToJSON } from './utils/spanUtils';\n\n/**\n * Apply SdkInfo (name, version, packages, integrations) to the corresponding event key.\n * Merge with existing data if any.\n **/\nfunction enhanceEventWithSdkInfo(event: Event, sdkInfo?: SdkInfo): Event {\n  if (!sdkInfo) {\n    return event;\n  }\n  event.sdk = event.sdk || {};\n  event.sdk.name = event.sdk.name || sdkInfo.name;\n  event.sdk.version = event.sdk.version || sdkInfo.version;\n  event.sdk.integrations = [...(event.sdk.integrations || []), ...(sdkInfo.integrations || [])];\n  event.sdk.packages = [...(event.sdk.packages || []), ...(sdkInfo.packages || [])];\n  return event;\n}\n\n/** Creates an envelope from a Session */\nexport function createSessionEnvelope(\n  session: Session | SessionAggregates,\n  dsn?: DsnComponents,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n): SessionEnvelope {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n  const envelopeHeaders = {\n    sent_at: new Date().toISOString(),\n    ...(sdkInfo && { sdk: sdkInfo }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n  };\n\n  const envelopeItem: SessionItem =\n    'aggregates' in session ? [{ type: 'sessions' }, session] : [{ type: 'session' }, session.toJSON()];\n\n  return createEnvelope<SessionEnvelope>(envelopeHeaders, [envelopeItem]);\n}\n\n/**\n * Create an Envelope from an event.\n */\nexport function createEventEnvelope(\n  event: Event,\n  dsn?: DsnComponents,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n): EventEnvelope {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n\n  /*\n    Note: Due to TS, event.type may be `replay_event`, theoretically.\n    In practice, we never call `createEventEnvelope` with `replay_event` type,\n    and we'd have to adjust a looot of types to make this work properly.\n    We want to avoid casting this around, as that could lead to bugs (e.g. when we add another type)\n    So the safe choice is to really guard against the replay_event type here.\n  */\n  const eventType = event.type && event.type !== 'replay_event' ? event.type : 'event';\n\n  enhanceEventWithSdkInfo(event, metadata?.sdk);\n\n  const envelopeHeaders = createEventEnvelopeHeaders(event, sdkInfo, tunnel, dsn);\n\n  // Prevent this data (which, if it exists, was used in earlier steps in the processing pipeline) from being sent to\n  // sentry. (Note: Our use of this property comes and goes with whatever we might be debugging, whatever hacks we may\n  // have temporarily added, etc. Even if we don't happen to be using it at some point in the future, let's not get rid\n  // of this `delete`, lest we miss putting it back in the next time the property is in use.)\n  delete event.sdkProcessingMetadata;\n\n  const eventItem: EventItem = [{ type: eventType }, event];\n  return createEnvelope<EventEnvelope>(envelopeHeaders, [eventItem]);\n}\n\n/**\n * Create envelope from Span item.\n *\n * Takes an optional client and runs spans through `beforeSendSpan` if available.\n */\nexport function createSpanEnvelope(spans: [SentrySpan, ...SentrySpan[]], client?: Client): SpanEnvelope {\n  function dscHasRequiredProps(dsc: Partial<DynamicSamplingContext>): dsc is DynamicSamplingContext {\n    return !!dsc.trace_id && !!dsc.public_key;\n  }\n\n  // For the moment we'll obtain the DSC from the first span in the array\n  // This might need to be changed if we permit sending multiple spans from\n  // different segments in one envelope\n  const dsc = getDynamicSamplingContextFromSpan(spans[0]);\n\n  const dsn = client?.getDsn();\n  const tunnel = client?.getOptions().tunnel;\n\n  const headers: SpanEnvelope[0] = {\n    sent_at: new Date().toISOString(),\n    ...(dscHasRequiredProps(dsc) && { trace: dsc }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n  };\n\n  const beforeSendSpan = client?.getOptions().beforeSendSpan;\n  const convertToSpanJSON = beforeSendSpan\n    ? (span: SentrySpan) => {\n        const spanJson = spanToJSON(span);\n        const processedSpan = beforeSendSpan(spanJson);\n\n        if (!processedSpan) {\n          showSpanDropWarning();\n          return spanJson;\n        }\n\n        return processedSpan;\n      }\n    : spanToJSON;\n\n  const items: SpanItem[] = [];\n  for (const span of spans) {\n    const spanJson = convertToSpanJSON(span);\n    if (spanJson) {\n      items.push(createSpanEnvelopeItem(spanJson));\n    }\n  }\n\n  return createEnvelope<SpanEnvelope>(headers, items);\n}\n\n/**\n * Create an Envelope from a CSP report.\n */\nexport function createRawSecurityEnvelope(\n  report: LegacyCSPReport,\n  dsn: DsnComponents,\n  tunnel?: string,\n  release?: string,\n  environment?: string,\n): RawSecurityEnvelope {\n  const envelopeHeaders = {\n    event_id: uuid4(),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n  };\n\n  const eventItem: RawSecurityItem = [\n    { type: 'raw_security', sentry_release: release, sentry_environment: environment },\n    report,\n  ];\n\n  return createEnvelope<RawSecurityEnvelope>(envelopeHeaders, [eventItem]);\n}\n"], "names": [], "mappings": ";;;;;AA8BA;AACA;AACA;AACA;AACA,SAAS,uBAAuB,CAAC,KAAK,EAAS,OAAO,EAAmB;AACzE,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,KAAK,CAAC,GAAA,GAAM,KAAK,CAAC,GAAA,IAAO,EAAE;AAC7B,EAAE,KAAK,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,GAAG,CAAC,IAAA,IAAQ,OAAO,CAAC,IAAI;AACjD,EAAE,KAAK,CAAC,GAAG,CAAC,UAAU,KAAK,CAAC,GAAG,CAAC,OAAA,IAAW,OAAO,CAAC,OAAO;AAC1D,EAAE,KAAK,CAAC,GAAG,CAAC,YAAA,GAAe,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,YAAA,IAAgB,EAAE,CAAC,EAAE,IAAI,OAAO,CAAC,YAAA,IAAgB,EAAE,CAAC,CAAC;AAC/F,EAAE,KAAK,CAAC,GAAG,CAAC,QAAA,GAAW,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,QAAA,IAAY,EAAE,CAAC,EAAE,IAAI,OAAO,CAAC,QAAA,IAAY,EAAE,CAAC,CAAC;AACnF,EAAE,OAAO,KAAK;AACd;;AAEA;AACO,SAAS,qBAAqB;AACrC,EAAE,OAAO;AACT,EAAE,GAAG;AACL,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAmB;AACnB,EAAE,MAAM,OAAA,GAAU,+BAA+B,CAAC,QAAQ,CAAC;AAC3D,EAAE,MAAM,kBAAkB;AAC1B,IAAI,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AACrC,IAAI,IAAI,OAAA,IAAW,EAAE,GAAG,EAAE,OAAA,EAAS,CAAC;AACpC,IAAI,IAAI,CAAC,CAAC,MAAA,IAAU,GAAA,IAAO,EAAE,GAAG,EAAE,WAAW,CAAC,GAAG,CAAA,EAAG,CAAC;AACrD,GAAG;;AAEH,EAAE,MAAM,YAAY;AACpB,IAAI,YAAA,IAAgB,OAAA,GAAU,CAAC,EAAE,IAAI,EAAE,UAAA,EAAY,EAAE,OAAO,CAAA,GAAI,CAAC,EAAE,IAAI,EAAE,SAAA,EAAW,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;;AAEvG,EAAE,OAAO,cAAc,CAAkB,eAAe,EAAE,CAAC,YAAY,CAAC,CAAC;AACzE;;AAEA;AACA;AACA;AACO,SAAS,mBAAmB;AACnC,EAAE,KAAK;AACP,EAAE,GAAG;AACL,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAiB;AACjB,EAAE,MAAM,OAAA,GAAU,+BAA+B,CAAC,QAAQ,CAAC;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,SAAA,GAAY,KAAK,CAAC,QAAQ,KAAK,CAAC,IAAA,KAAS,cAAA,GAAiB,KAAK,CAAC,IAAA,GAAO,OAAO;;AAEtF,EAAE,uBAAuB,CAAC,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC;;AAE/C,EAAE,MAAM,eAAA,GAAkB,0BAA0B,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC;;AAEjF;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,CAAC,qBAAqB;;AAEpC,EAAE,MAAM,SAAS,GAAc,CAAC,EAAE,IAAI,EAAE,SAAA,EAAW,EAAE,KAAK,CAAC;AAC3D,EAAE,OAAO,cAAc,CAAgB,eAAe,EAAE,CAAC,SAAS,CAAC,CAAC;AACpE;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,KAAK,EAAiC,MAAM,EAAyB;AACxG,EAAE,SAAS,mBAAmB,CAAC,GAAG,EAAkE;AACpG,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,QAAA,IAAY,CAAC,CAAC,GAAG,CAAC,UAAU;AAC7C;;AAEA;AACA;AACA;AACA,EAAE,MAAM,MAAM,iCAAiC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;AAEzD,EAAE,MAAM,GAAA,GAAM,MAAM,EAAE,MAAM,EAAE;AAC9B,EAAE,MAAM,SAAS,MAAM,EAAE,UAAU,EAAE,CAAC,MAAM;;AAE5C,EAAE,MAAM,OAAO,GAAoB;AACnC,IAAI,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AACrC,IAAI,IAAI,mBAAmB,CAAC,GAAG,CAAA,IAAK,EAAE,KAAK,EAAE,GAAA,EAAK,CAAC;AACnD,IAAI,IAAI,CAAC,CAAC,MAAA,IAAU,GAAA,IAAO,EAAE,GAAG,EAAE,WAAW,CAAC,GAAG,CAAA,EAAG,CAAC;AACrD,GAAG;;AAEH,EAAE,MAAM,iBAAiB,MAAM,EAAE,UAAU,EAAE,CAAC,cAAc;AAC5D,EAAE,MAAM,oBAAoB;AAC5B,MAAM,CAAC,IAAI,KAAiB;AAC5B,QAAQ,MAAM,QAAA,GAAW,UAAU,CAAC,IAAI,CAAC;AACzC,QAAQ,MAAM,aAAA,GAAgB,cAAc,CAAC,QAAQ,CAAC;;AAEtD,QAAQ,IAAI,CAAC,aAAa,EAAE;AAC5B,UAAU,mBAAmB,EAAE;AAC/B,UAAU,OAAO,QAAQ;AACzB;;AAEA,QAAQ,OAAO,aAAa;AAC5B;AACA,MAAM,UAAU;;AAEhB,EAAE,MAAM,KAAK,GAAe,EAAE;AAC9B,EAAE,KAAK,MAAM,IAAA,IAAQ,KAAK,EAAE;AAC5B,IAAI,MAAM,QAAA,GAAW,iBAAiB,CAAC,IAAI,CAAC;AAC5C,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;AAClD;AACA;;AAEA,EAAE,OAAO,cAAc,CAAe,OAAO,EAAE,KAAK,CAAC;AACrD;;;;"}