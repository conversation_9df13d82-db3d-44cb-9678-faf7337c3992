"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(req) {\n    const supabaseResponse = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n        request: req\n    });\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://xpcbyzcaidfukddqniny.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTA1MzYsImV4cCI6MjA2MjM2NjUzNn0.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A\", {\n        cookies: {\n            getAll () {\n                return req.cookies.getAll();\n            },\n            setAll (cookiesToSet) {\n                cookiesToSet.forEach(({ name, value, options })=>{\n                    req.cookies.set(name, value);\n                    supabaseResponse.cookies.set(name, value, options);\n                });\n            }\n        }\n    });\n    // This will refresh session if expired - required for Server Components\n    await supabase.auth.getUser();\n    // Protected routes that require authentication\n    const protectedPaths = [\n        '/dashboard',\n        '/projects',\n        '/content',\n        '/analytics'\n    ];\n    const authPaths = [\n        '/auth/signin',\n        '/auth/signup',\n        '/auth/callback'\n    ];\n    const isProtectedPath = protectedPaths.some((path)=>req.nextUrl.pathname.startsWith(path));\n    const isAuthPath = authPaths.some((path)=>req.nextUrl.pathname.startsWith(path));\n    // Get the current user\n    const { data: { user } } = await supabase.auth.getUser();\n    // Redirect to signin if accessing protected route without user\n    if (isProtectedPath && !user) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL('/auth/signin', req.url));\n    }\n    // Redirect to dashboard if accessing auth pages while logged in\n    if (isAuthPath && user) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL('/dashboard', req.url));\n    }\n    return supabaseResponse;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */ '/((?!api|_next/static|_next/image|favicon.ico).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});