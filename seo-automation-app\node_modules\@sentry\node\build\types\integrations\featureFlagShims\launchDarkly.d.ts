/**
 * This is a shim for the LaunchDarkly integration.
 * We need this in order to not throw runtime errors when accidentally importing this on the server through a meta framework like Next.js.
 */
export declare const launchDarklyIntegrationShim: (_options?: unknown) => import("@sentry/core").Integration;
/**
 * This is a shim for the LaunchDarkly flag used handler.
 */
export declare function buildLaunchDarklyFlagUsedHandlerShim(): unknown;
//# sourceMappingURL=launchDarkly.d.ts.map