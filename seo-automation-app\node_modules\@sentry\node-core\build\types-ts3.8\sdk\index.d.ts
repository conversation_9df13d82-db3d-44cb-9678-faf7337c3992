import { Integration } from '@sentry/core';
import { NodeOptions } from '../types';
import { NodeClient } from './client';
/**
 * Get default integrations for the Node-Core SDK.
 */
export declare function getDefaultIntegrations(): Integration[];
/**
 * Initialize Sentry for Node.
 */
export declare function init(options?: NodeOptions | undefined): NodeClient | undefined;
/**
 * Initialize Sentry for Node, without any integrations added by default.
 */
export declare function initWithoutDefaultIntegrations(options?: NodeOptions | undefined): NodeClient;
/**
 * Validate that your OpenTelemetry setup is correct.
 */
export declare function validateOpenTelemetrySetup(): void;
//# sourceMappingURL=index.d.ts.map
