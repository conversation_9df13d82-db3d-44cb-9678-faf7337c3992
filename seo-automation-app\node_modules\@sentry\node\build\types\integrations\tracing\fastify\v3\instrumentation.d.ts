import { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
import type { FastifyInstrumentationConfig } from './types';
/**
 * Fastify instrumentation for OpenTelemetry
 */
export declare class FastifyInstrumentationV3 extends InstrumentationBase<FastifyInstrumentationConfig> {
    constructor(config?: FastifyInstrumentationConfig);
    init(): InstrumentationNodeModuleDefinition[];
    private _hookOnRequest;
    private _wrapHandler;
    private _wrapAddHook;
    private _patchConstructor;
    private _patchSend;
    private _hookPreHandler;
}
//# sourceMappingURL=instrumentation.d.ts.map