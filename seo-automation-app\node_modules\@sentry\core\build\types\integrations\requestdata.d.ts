interface RequestDataIncludeOptions {
    cookies?: boolean;
    data?: boolean;
    headers?: boolean;
    ip?: boolean;
    query_string?: boolean;
    url?: boolean;
}
type RequestDataIntegrationOptions = {
    /**
     * Controls what data is pulled from the request and added to the event.
     */
    include?: RequestDataIncludeOptions;
};
/**
 * Add data about a request to an event. Primarily for use in Node-based SDKs, but included in `@sentry/core`
 * so it can be used in cross-platform SDKs like `@sentry/nextjs`.
 */
export declare const requestDataIntegration: (options?: RequestDataIntegrationOptions | undefined) => import("../types-hoist/integration").Integration;
export {};
//# sourceMappingURL=requestdata.d.ts.map