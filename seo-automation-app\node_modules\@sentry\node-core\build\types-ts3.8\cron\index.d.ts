import { instrumentCron } from './cron';
import { instrumentNodeCron } from './node-cron';
import { instrumentNodeSchedule } from './node-schedule';
/** Methods to instrument cron libraries for Sentry check-ins */
export declare const cron: {
    instrumentCron: typeof instrumentCron;
    instrumentNodeCron: typeof instrumentNodeCron;
    instrumentNodeSchedule: typeof instrumentNodeSchedule;
};
//# sourceMappingURL=index.d.ts.map
