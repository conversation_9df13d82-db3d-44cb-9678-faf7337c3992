"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "IconsMetadata", {
    enumerable: true,
    get: function() {
        return IconsMetadata;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _iconmark = require("./icon-mark");
const _meta = require("./meta");
function IconDescriptorLink({ icon }) {
    const { url, rel = 'icon', ...props } = icon;
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("link", {
        rel: rel,
        href: url.toString(),
        ...props
    });
}
function IconLink({ rel, icon }) {
    if (typeof icon === 'object' && !(icon instanceof URL)) {
        if (!icon.rel && rel) icon.rel = rel;
        return IconDescriptorLink({
            icon
        });
    } else {
        const href = icon.toString();
        return /*#__PURE__*/ (0, _jsxruntime.jsx)("link", {
            rel: rel,
            href: href
        });
    }
}
function IconsMetadata({ icons }) {
    if (!icons) return null;
    const shortcutList = icons.shortcut;
    const iconList = icons.icon;
    const appleList = icons.apple;
    const otherList = icons.other;
    const hasIcon = Boolean((shortcutList == null ? void 0 : shortcutList.length) || (iconList == null ? void 0 : iconList.length) || (appleList == null ? void 0 : appleList.length) || (otherList == null ? void 0 : otherList.length));
    if (!hasIcon) return null;
    return (0, _meta.MetaFilter)([
        shortcutList ? shortcutList.map((icon)=>IconLink({
                rel: 'shortcut icon',
                icon
            })) : null,
        iconList ? iconList.map((icon)=>IconLink({
                rel: 'icon',
                icon
            })) : null,
        appleList ? appleList.map((icon)=>IconLink({
                rel: 'apple-touch-icon',
                icon
            })) : null,
        otherList ? otherList.map((icon)=>IconDescriptorLink({
                icon
            })) : null,
        hasIcon ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_iconmark.IconMark, {}) : null
    ]);
}

//# sourceMappingURL=icons.js.map