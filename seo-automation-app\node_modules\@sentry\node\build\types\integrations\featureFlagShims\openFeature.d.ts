/**
 * This is a shim for the OpenFeature integration.
 * We need this in order to not throw runtime errors when accidentally importing this on the server through a meta framework like Next.js.
 */
export declare const openFeatureIntegrationShim: (_options?: unknown) => import("@sentry/core").Integration;
/**
 * This is a shim for the OpenFeature integration hook.
 */
export declare class OpenFeatureIntegrationHookShim {
    /**
     *
     */
    constructor();
    /**
     *
     */
    after(): void;
    /**
     *
     */
    error(): void;
}
//# sourceMappingURL=openFeature.d.ts.map