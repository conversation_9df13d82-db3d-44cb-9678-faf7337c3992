type SpotlightConnectionOptions = {
    /**
     * Set this if the Spotlight Sidecar is not running on localhost:8969
     * By default, the Url is set to http://localhost:8969/stream
     */
    sidecarUrl?: string;
};
export declare const INTEGRATION_NAME = "Spotlight";
/**
 * Use this integration to send errors and transactions to Spotlight.
 *
 * Learn more about spotlight at https://spotlightjs.com
 *
 * Important: This integration only works with Node 18 or newer.
 */
export declare const spotlightIntegration: (options?: Partial<SpotlightConnectionOptions> | undefined) => import("@sentry/core").Integration;
export {};
//# sourceMappingURL=spotlight.d.ts.map
