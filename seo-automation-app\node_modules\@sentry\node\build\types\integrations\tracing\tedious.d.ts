import { TediousInstrumentation } from '@opentelemetry/instrumentation-tedious';
export declare const instrumentTedious: ((options?: unknown) => TediousInstrumentation) & {
    id: string;
};
/**
 * Adds Sentry tracing instrumentation for the [tedious](https://www.npmjs.com/package/tedious) library.
 *
 * For more information, see the [`tediousIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/tedious/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.tediousIntegration()],
 * });
 * ```
 */
export declare const tediousIntegration: () => import("@sentry/core").Integration;
//# sourceMappingURL=tedious.d.ts.map