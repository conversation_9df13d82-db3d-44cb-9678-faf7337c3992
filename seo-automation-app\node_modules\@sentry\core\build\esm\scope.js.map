{"version": 3, "file": "scope.js", "sources": ["../../src/scope.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type { Client } from './client';\nimport { DEBUG_BUILD } from './debug-build';\nimport { updateSession } from './session';\nimport type { Attachment } from './types-hoist/attachment';\nimport type { Breadcrumb } from './types-hoist/breadcrumb';\nimport type { Context, Contexts } from './types-hoist/context';\nimport type { DynamicSamplingContext } from './types-hoist/envelope';\nimport type { Event, EventHint } from './types-hoist/event';\nimport type { EventProcessor } from './types-hoist/eventprocessor';\nimport type { Extra, Extras } from './types-hoist/extra';\nimport type { Primitive } from './types-hoist/misc';\nimport type { RequestEventData } from './types-hoist/request';\nimport type { Session } from './types-hoist/session';\nimport type { SeverityLevel } from './types-hoist/severity';\nimport type { Span } from './types-hoist/span';\nimport type { PropagationContext } from './types-hoist/tracing';\nimport type { User } from './types-hoist/user';\nimport { isPlainObject } from './utils/is';\nimport { debug } from './utils/logger';\nimport { merge } from './utils/merge';\nimport { uuid4 } from './utils/misc';\nimport { generateTraceId } from './utils/propagationContext';\nimport { _getSpanForScope, _setSpanForScope } from './utils/spanOnScope';\nimport { truncate } from './utils/string';\nimport { dateTimestampInSeconds } from './utils/time';\n\n/**\n * Default value for maximum number of breadcrumbs added to an event.\n */\nconst DEFAULT_MAX_BREADCRUMBS = 100;\n\n/**\n * A context to be used for capturing an event.\n * This can either be a Scope, or a partial ScopeContext,\n * or a callback that receives the current scope and returns a new scope to use.\n */\nexport type CaptureContext = Scope | Partial<ScopeContext> | ((scope: Scope) => Scope);\n\n/**\n * Data that can be converted to a Scope.\n */\nexport interface ScopeContext {\n  user: User;\n  level: SeverityLevel;\n  extra: Extras;\n  contexts: Contexts;\n  tags: { [key: string]: Primitive };\n  fingerprint: string[];\n  propagationContext: PropagationContext;\n}\n\nexport interface SdkProcessingMetadata {\n  [key: string]: unknown;\n  requestSession?: {\n    status: 'ok' | 'errored' | 'crashed';\n  };\n  normalizedRequest?: RequestEventData;\n  dynamicSamplingContext?: Partial<DynamicSamplingContext>;\n  capturedSpanScope?: Scope;\n  capturedSpanIsolationScope?: Scope;\n  spanCountBeforeProcessing?: number;\n  ipAddress?: string;\n}\n\n/**\n * Normalized data of the Scope, ready to be used.\n */\nexport interface ScopeData {\n  eventProcessors: EventProcessor[];\n  breadcrumbs: Breadcrumb[];\n  user: User;\n  tags: { [key: string]: Primitive };\n  extra: Extras;\n  contexts: Contexts;\n  attachments: Attachment[];\n  propagationContext: PropagationContext;\n  sdkProcessingMetadata: SdkProcessingMetadata;\n  fingerprint: string[];\n  level?: SeverityLevel;\n  transactionName?: string;\n  span?: Span;\n}\n\n/**\n * Holds additional event information.\n */\nexport class Scope {\n  /** Flag if notifying is happening. */\n  protected _notifyingListeners: boolean;\n\n  /** Callback for client to receive scope changes. */\n  protected _scopeListeners: Array<(scope: Scope) => void>;\n\n  /** Callback list that will be called during event processing. */\n  protected _eventProcessors: EventProcessor[];\n\n  /** Array of breadcrumbs. */\n  protected _breadcrumbs: Breadcrumb[];\n\n  /** User */\n  protected _user: User;\n\n  /** Tags */\n  protected _tags: { [key: string]: Primitive };\n\n  /** Extra */\n  protected _extra: Extras;\n\n  /** Contexts */\n  protected _contexts: Contexts;\n\n  /** Attachments */\n  protected _attachments: Attachment[];\n\n  /** Propagation Context for distributed tracing */\n  protected _propagationContext: PropagationContext;\n\n  /**\n   * A place to stash data which is needed at some point in the SDK's event processing pipeline but which shouldn't get\n   * sent to Sentry\n   */\n  protected _sdkProcessingMetadata: SdkProcessingMetadata;\n\n  /** Fingerprint */\n  protected _fingerprint?: string[];\n\n  /** Severity */\n  protected _level?: SeverityLevel;\n\n  /**\n   * Transaction Name\n   *\n   * IMPORTANT: The transaction name on the scope has nothing to do with root spans/transaction objects.\n   * It's purpose is to assign a transaction to the scope that's added to non-transaction events.\n   */\n  protected _transactionName?: string;\n\n  /** Session */\n  protected _session?: Session;\n\n  /** The client on this scope */\n  protected _client?: Client;\n\n  /** Contains the last event id of a captured event.  */\n  protected _lastEventId?: string;\n\n  // NOTE: Any field which gets added here should get added not only to the constructor but also to the `clone` method.\n\n  public constructor() {\n    this._notifyingListeners = false;\n    this._scopeListeners = [];\n    this._eventProcessors = [];\n    this._breadcrumbs = [];\n    this._attachments = [];\n    this._user = {};\n    this._tags = {};\n    this._extra = {};\n    this._contexts = {};\n    this._sdkProcessingMetadata = {};\n    this._propagationContext = {\n      traceId: generateTraceId(),\n      sampleRand: Math.random(),\n    };\n  }\n\n  /**\n   * Clone all data from this scope into a new scope.\n   */\n  public clone(): Scope {\n    const newScope = new Scope();\n    newScope._breadcrumbs = [...this._breadcrumbs];\n    newScope._tags = { ...this._tags };\n    newScope._extra = { ...this._extra };\n    newScope._contexts = { ...this._contexts };\n    if (this._contexts.flags) {\n      // We need to copy the `values` array so insertions on a cloned scope\n      // won't affect the original array.\n      newScope._contexts.flags = {\n        values: [...this._contexts.flags.values],\n      };\n    }\n\n    newScope._user = this._user;\n    newScope._level = this._level;\n    newScope._session = this._session;\n    newScope._transactionName = this._transactionName;\n    newScope._fingerprint = this._fingerprint;\n    newScope._eventProcessors = [...this._eventProcessors];\n    newScope._attachments = [...this._attachments];\n    newScope._sdkProcessingMetadata = { ...this._sdkProcessingMetadata };\n    newScope._propagationContext = { ...this._propagationContext };\n    newScope._client = this._client;\n    newScope._lastEventId = this._lastEventId;\n\n    _setSpanForScope(newScope, _getSpanForScope(this));\n\n    return newScope;\n  }\n\n  /**\n   * Update the client assigned to this scope.\n   * Note that not every scope will have a client assigned - isolation scopes & the global scope will generally not have a client,\n   * as well as manually created scopes.\n   */\n  public setClient(client: Client | undefined): void {\n    this._client = client;\n  }\n\n  /**\n   * Set the ID of the last captured error event.\n   * This is generally only captured on the isolation scope.\n   */\n  public setLastEventId(lastEventId: string | undefined): void {\n    this._lastEventId = lastEventId;\n  }\n\n  /**\n   * Get the client assigned to this scope.\n   */\n  public getClient<C extends Client>(): C | undefined {\n    return this._client as C | undefined;\n  }\n\n  /**\n   * Get the ID of the last captured error event.\n   * This is generally only available on the isolation scope.\n   */\n  public lastEventId(): string | undefined {\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addScopeListener(callback: (scope: Scope) => void): void {\n    this._scopeListeners.push(callback);\n  }\n\n  /**\n   * Add an event processor that will be called before an event is sent.\n   */\n  public addEventProcessor(callback: EventProcessor): this {\n    this._eventProcessors.push(callback);\n    return this;\n  }\n\n  /**\n   * Set the user for this scope.\n   * Set to `null` to unset the user.\n   */\n  public setUser(user: User | null): this {\n    // If null is passed we want to unset everything, but still define keys,\n    // so that later down in the pipeline any existing values are cleared.\n    this._user = user || {\n      email: undefined,\n      id: undefined,\n      ip_address: undefined,\n      username: undefined,\n    };\n\n    if (this._session) {\n      updateSession(this._session, { user });\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Get the user from this scope.\n   */\n  public getUser(): User | undefined {\n    return this._user;\n  }\n\n  /**\n   * Set an object that will be merged into existing tags on the scope,\n   * and will be sent as tags data with the event.\n   */\n  public setTags(tags: { [key: string]: Primitive }): this {\n    this._tags = {\n      ...this._tags,\n      ...tags,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set a single tag that will be sent as tags data with the event.\n   */\n  public setTag(key: string, value: Primitive): this {\n    this._tags = { ...this._tags, [key]: value };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set an object that will be merged into existing extra on the scope,\n   * and will be sent as extra data with the event.\n   */\n  public setExtras(extras: Extras): this {\n    this._extra = {\n      ...this._extra,\n      ...extras,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set a single key:value extra entry that will be sent as extra data with the event.\n   */\n  public setExtra(key: string, extra: Extra): this {\n    this._extra = { ...this._extra, [key]: extra };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the fingerprint on the scope to send with the events.\n   * @param {string[]} fingerprint Fingerprint to group events in Sentry.\n   */\n  public setFingerprint(fingerprint: string[]): this {\n    this._fingerprint = fingerprint;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the level on the scope for future events.\n   */\n  public setLevel(level: SeverityLevel): this {\n    this._level = level;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the transaction name on the scope so that the name of e.g. taken server route or\n   * the page location is attached to future events.\n   *\n   * IMPORTANT: Calling this function does NOT change the name of the currently active\n   * root span. If you want to change the name of the active root span, use\n   * `Sentry.updateSpanName(rootSpan, 'new name')` instead.\n   *\n   * By default, the SDK updates the scope's transaction name automatically on sensible\n   * occasions, such as a page navigation or when handling a new request on the server.\n   */\n  public setTransactionName(name?: string): this {\n    this._transactionName = name;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets context data with the given name.\n   * Data passed as context will be normalized. You can also pass `null` to unset the context.\n   * Note that context data will not be merged - calling `setContext` will overwrite an existing context with the same key.\n   */\n  public setContext(key: string, context: Context | null): this {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts[key] = context;\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set the session for the scope.\n   */\n  public setSession(session?: Session): this {\n    if (!session) {\n      delete this._session;\n    } else {\n      this._session = session;\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Get the session from the scope.\n   */\n  public getSession(): Session | undefined {\n    return this._session;\n  }\n\n  /**\n   * Updates the scope with provided data. Can work in three variations:\n   * - plain object containing updatable attributes\n   * - Scope instance that'll extract the attributes from\n   * - callback function that'll receive the current scope as an argument and allow for modifications\n   */\n  public update(captureContext?: CaptureContext): this {\n    if (!captureContext) {\n      return this;\n    }\n\n    const scopeToMerge = typeof captureContext === 'function' ? captureContext(this) : captureContext;\n\n    const scopeInstance =\n      scopeToMerge instanceof Scope\n        ? scopeToMerge.getScopeData()\n        : isPlainObject(scopeToMerge)\n          ? (captureContext as ScopeContext)\n          : undefined;\n\n    const { tags, extra, user, contexts, level, fingerprint = [], propagationContext } = scopeInstance || {};\n\n    this._tags = { ...this._tags, ...tags };\n    this._extra = { ...this._extra, ...extra };\n    this._contexts = { ...this._contexts, ...contexts };\n\n    if (user && Object.keys(user).length) {\n      this._user = user;\n    }\n\n    if (level) {\n      this._level = level;\n    }\n\n    if (fingerprint.length) {\n      this._fingerprint = fingerprint;\n    }\n\n    if (propagationContext) {\n      this._propagationContext = propagationContext;\n    }\n\n    return this;\n  }\n\n  /**\n   * Clears the current scope and resets its properties.\n   * Note: The client will not be cleared.\n   */\n  public clear(): this {\n    // client is not cleared here on purpose!\n    this._breadcrumbs = [];\n    this._tags = {};\n    this._extra = {};\n    this._user = {};\n    this._contexts = {};\n    this._level = undefined;\n    this._transactionName = undefined;\n    this._fingerprint = undefined;\n    this._session = undefined;\n    _setSpanForScope(this, undefined);\n    this._attachments = [];\n    this.setPropagationContext({ traceId: generateTraceId(), sampleRand: Math.random() });\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Adds a breadcrumb to the scope.\n   * By default, the last 100 breadcrumbs are kept.\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, maxBreadcrumbs?: number): this {\n    const maxCrumbs = typeof maxBreadcrumbs === 'number' ? maxBreadcrumbs : DEFAULT_MAX_BREADCRUMBS;\n\n    // No data has been changed, so don't notify scope listeners\n    if (maxCrumbs <= 0) {\n      return this;\n    }\n\n    const mergedBreadcrumb: Breadcrumb = {\n      timestamp: dateTimestampInSeconds(),\n      ...breadcrumb,\n      // Breadcrumb messages can theoretically be infinitely large and they're held in memory so we truncate them not to leak (too much) memory\n      message: breadcrumb.message ? truncate(breadcrumb.message, 2048) : breadcrumb.message,\n    };\n\n    this._breadcrumbs.push(mergedBreadcrumb);\n    if (this._breadcrumbs.length > maxCrumbs) {\n      this._breadcrumbs = this._breadcrumbs.slice(-maxCrumbs);\n      this._client?.recordDroppedEvent('buffer_overflow', 'log_item');\n    }\n\n    this._notifyScopeListeners();\n\n    return this;\n  }\n\n  /**\n   * Get the last breadcrumb of the scope.\n   */\n  public getLastBreadcrumb(): Breadcrumb | undefined {\n    return this._breadcrumbs[this._breadcrumbs.length - 1];\n  }\n\n  /**\n   * Clear all breadcrumbs from the scope.\n   */\n  public clearBreadcrumbs(): this {\n    this._breadcrumbs = [];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Add an attachment to the scope.\n   */\n  public addAttachment(attachment: Attachment): this {\n    this._attachments.push(attachment);\n    return this;\n  }\n\n  /**\n   * Clear all attachments from the scope.\n   */\n  public clearAttachments(): this {\n    this._attachments = [];\n    return this;\n  }\n\n  /**\n   * Get the data of this scope, which should be applied to an event during processing.\n   */\n  public getScopeData(): ScopeData {\n    return {\n      breadcrumbs: this._breadcrumbs,\n      attachments: this._attachments,\n      contexts: this._contexts,\n      tags: this._tags,\n      extra: this._extra,\n      user: this._user,\n      level: this._level,\n      fingerprint: this._fingerprint || [],\n      eventProcessors: this._eventProcessors,\n      propagationContext: this._propagationContext,\n      sdkProcessingMetadata: this._sdkProcessingMetadata,\n      transactionName: this._transactionName,\n      span: _getSpanForScope(this),\n    };\n  }\n\n  /**\n   * Add data which will be accessible during event processing but won't get sent to Sentry.\n   */\n  public setSDKProcessingMetadata(newData: SdkProcessingMetadata): this {\n    this._sdkProcessingMetadata = merge(this._sdkProcessingMetadata, newData, 2);\n    return this;\n  }\n\n  /**\n   * Add propagation context to the scope, used for distributed tracing\n   */\n  public setPropagationContext(context: PropagationContext): this {\n    this._propagationContext = context;\n    return this;\n  }\n\n  /**\n   * Get propagation context from the scope, used for distributed tracing\n   */\n  public getPropagationContext(): PropagationContext {\n    return this._propagationContext;\n  }\n\n  /**\n   * Capture an exception for this scope.\n   *\n   * @returns {string} The id of the captured Sentry event.\n   */\n  public captureException(exception: unknown, hint?: EventHint): string {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      DEBUG_BUILD && debug.warn('No client configured on scope - will not capture exception!');\n      return eventId;\n    }\n\n    const syntheticException = new Error('Sentry syntheticException');\n\n    this._client.captureException(\n      exception,\n      {\n        originalException: exception,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * Capture a message for this scope.\n   *\n   * @returns {string} The id of the captured message.\n   */\n  public captureMessage(message: string, level?: SeverityLevel, hint?: EventHint): string {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      DEBUG_BUILD && debug.warn('No client configured on scope - will not capture message!');\n      return eventId;\n    }\n\n    const syntheticException = new Error(message);\n\n    this._client.captureMessage(\n      message,\n      level,\n      {\n        originalException: message,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * Capture a Sentry event for this scope.\n   *\n   * @returns {string} The id of the captured event.\n   */\n  public captureEvent(event: Event, hint?: EventHint): string {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      DEBUG_BUILD && debug.warn('No client configured on scope - will not capture event!');\n      return eventId;\n    }\n\n    this._client.captureEvent(event, { ...hint, event_id: eventId }, this);\n\n    return eventId;\n  }\n\n  /**\n   * This will be called on every set call.\n   */\n  protected _notifyScopeListeners(): void {\n    // We need this check for this._notifyingListeners to be able to work on scope during updates\n    // If this check is not here we'll produce endless recursion when something is done with the scope\n    // during the callback.\n    if (!this._notifyingListeners) {\n      this._notifyingListeners = true;\n      this._scopeListeners.forEach(callback => {\n        callback(this);\n      });\n      this._notifyingListeners = false;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AA2BA;AACA;AACA;AACA,MAAM,uBAAA,GAA0B,GAAG;;AAEnC;AACA;AACA;AACA;AACA;;AAgDA;AACA;AACA;AACO,MAAM,KAAA,CAAM;AACnB;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;AACA;AACA;AACA;;AAGA;;AAGA;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAGA;;AAGA;;AAGA;;AAGA;;AAEA,GAAS,WAAW,GAAG;AACvB,IAAI,IAAI,CAAC,mBAAA,GAAsB,KAAK;AACpC,IAAI,IAAI,CAAC,eAAA,GAAkB,EAAE;AAC7B,IAAI,IAAI,CAAC,gBAAA,GAAmB,EAAE;AAC9B,IAAI,IAAI,CAAC,YAAA,GAAe,EAAE;AAC1B,IAAI,IAAI,CAAC,YAAA,GAAe,EAAE;AAC1B,IAAI,IAAI,CAAC,KAAA,GAAQ,EAAE;AACnB,IAAI,IAAI,CAAC,KAAA,GAAQ,EAAE;AACnB,IAAI,IAAI,CAAC,MAAA,GAAS,EAAE;AACpB,IAAI,IAAI,CAAC,SAAA,GAAY,EAAE;AACvB,IAAI,IAAI,CAAC,sBAAA,GAAyB,EAAE;AACpC,IAAI,IAAI,CAAC,mBAAA,GAAsB;AAC/B,MAAM,OAAO,EAAE,eAAe,EAAE;AAChC,MAAM,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE;AAC/B,KAAK;AACL;;AAEA;AACA;AACA;AACA,GAAS,KAAK,GAAU;AACxB,IAAI,MAAM,QAAA,GAAW,IAAI,KAAK,EAAE;AAChC,IAAI,QAAQ,CAAC,YAAA,GAAe,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AAClD,IAAI,QAAQ,CAAC,KAAA,GAAQ,EAAE,GAAG,IAAI,CAAC,KAAA,EAAO;AACtC,IAAI,QAAQ,CAAC,MAAA,GAAS,EAAE,GAAG,IAAI,CAAC,MAAA,EAAQ;AACxC,IAAI,QAAQ,CAAC,SAAA,GAAY,EAAE,GAAG,IAAI,CAAC,SAAA,EAAW;AAC9C,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AAC9B;AACA;AACA,MAAM,QAAQ,CAAC,SAAS,CAAC,QAAQ;AACjC,QAAQ,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC;AAChD,OAAO;AACP;;AAEA,IAAI,QAAQ,CAAC,KAAA,GAAQ,IAAI,CAAC,KAAK;AAC/B,IAAI,QAAQ,CAAC,MAAA,GAAS,IAAI,CAAC,MAAM;AACjC,IAAI,QAAQ,CAAC,QAAA,GAAW,IAAI,CAAC,QAAQ;AACrC,IAAI,QAAQ,CAAC,gBAAA,GAAmB,IAAI,CAAC,gBAAgB;AACrD,IAAI,QAAQ,CAAC,YAAA,GAAe,IAAI,CAAC,YAAY;AAC7C,IAAI,QAAQ,CAAC,gBAAA,GAAmB,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC1D,IAAI,QAAQ,CAAC,YAAA,GAAe,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AAClD,IAAI,QAAQ,CAAC,sBAAA,GAAyB,EAAE,GAAG,IAAI,CAAC,sBAAA,EAAwB;AACxE,IAAI,QAAQ,CAAC,mBAAA,GAAsB,EAAE,GAAG,IAAI,CAAC,mBAAA,EAAqB;AAClE,IAAI,QAAQ,CAAC,OAAA,GAAU,IAAI,CAAC,OAAO;AACnC,IAAI,QAAQ,CAAC,YAAA,GAAe,IAAI,CAAC,YAAY;;AAE7C,IAAI,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC;;AAEtD,IAAI,OAAO,QAAQ;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAS,SAAS,CAAC,MAAM,EAA4B;AACrD,IAAI,IAAI,CAAC,OAAA,GAAU,MAAM;AACzB;;AAEA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAC,WAAW,EAA4B;AAC/D,IAAI,IAAI,CAAC,YAAA,GAAe,WAAW;AACnC;;AAEA;AACA;AACA;AACA,GAAS,SAAS,GAAoC;AACtD,IAAI,OAAO,IAAI,CAAC,OAAA;AAChB;;AAEA;AACA;AACA;AACA;AACA,GAAS,WAAW,GAAuB;AAC3C,IAAI,OAAO,IAAI,CAAC,YAAY;AAC5B;;AAEA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,QAAQ,EAAgC;AAClE,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC;AACvC;;AAEA;AACA;AACA;AACA,GAAS,iBAAiB,CAAC,QAAQ,EAAwB;AAC3D,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC;AACxC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAqB;AAC1C;AACA;AACA,IAAI,IAAI,CAAC,KAAA,GAAQ,QAAQ;AACzB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,UAAU,EAAE,SAAS;AAC3B,MAAM,QAAQ,EAAE,SAAS;AACzB,KAAK;;AAEL,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAA,EAAM,CAAC;AAC5C;;AAEA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,OAAO,GAAqB;AACrC,IAAI,OAAO,IAAI,CAAC,KAAK;AACrB;;AAEA;AACA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAsC;AAC3D,IAAI,IAAI,CAAC,KAAA,GAAQ;AACjB,MAAM,GAAG,IAAI,CAAC,KAAK;AACnB,MAAM,GAAG,IAAI;AACb,KAAK;AACL,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,MAAM,CAAC,GAAG,EAAU,KAAK,EAAmB;AACrD,IAAI,IAAI,CAAC,KAAA,GAAQ,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,OAAO;AAChD,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA,GAAS,SAAS,CAAC,MAAM,EAAgB;AACzC,IAAI,IAAI,CAAC,MAAA,GAAS;AAClB,MAAM,GAAG,IAAI,CAAC,MAAM;AACpB,MAAM,GAAG,MAAM;AACf,KAAK;AACL,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,QAAQ,CAAC,GAAG,EAAU,KAAK,EAAe;AACnD,IAAI,IAAI,CAAC,MAAA,GAAS,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,OAAO;AAClD,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAC,WAAW,EAAkB;AACrD,IAAI,IAAI,CAAC,YAAA,GAAe,WAAW;AACnC,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,QAAQ,CAAC,KAAK,EAAuB;AAC9C,IAAI,IAAI,CAAC,MAAA,GAAS,KAAK;AACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,kBAAkB,CAAC,IAAI,EAAiB;AACjD,IAAI,IAAI,CAAC,gBAAA,GAAmB,IAAI;AAChC,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAS,UAAU,CAAC,GAAG,EAAU,OAAO,EAAwB;AAChE,IAAI,IAAI,OAAA,KAAY,IAAI,EAAE;AAC1B;AACA,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;AAChC,WAAW;AACX,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAA,GAAI,OAAO;AACnC;;AAEA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,UAAU,CAAC,OAAO,EAAkB;AAC7C,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,QAAQ;AAC1B,WAAW;AACX,MAAM,IAAI,CAAC,QAAA,GAAW,OAAO;AAC7B;AACA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,UAAU,GAAwB;AAC3C,IAAI,OAAO,IAAI,CAAC,QAAQ;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,MAAM,CAAC,cAAc,EAAyB;AACvD,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO,IAAI;AACjB;;AAEA,IAAI,MAAM,YAAA,GAAe,OAAO,cAAA,KAAmB,UAAA,GAAa,cAAc,CAAC,IAAI,CAAA,GAAI,cAAc;;AAErG,IAAI,MAAM,aAAA;AACV,MAAM,wBAAwB;AAC9B,UAAU,YAAY,CAAC,YAAY;AACnC,UAAU,aAAa,CAAC,YAAY;AACpC,aAAa,cAAA;AACb,YAAY,SAAS;;AAErB,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAA,GAAc,EAAE,EAAE,kBAAA,KAAuB,aAAA,IAAiB,EAAE;;AAE5G,IAAI,IAAI,CAAC,KAAA,GAAQ,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAA,EAAM;AAC3C,IAAI,IAAI,CAAC,MAAA,GAAS,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAA,EAAO;AAC9C,IAAI,IAAI,CAAC,SAAA,GAAY,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,QAAA,EAAU;;AAEvD,IAAI,IAAI,IAAA,IAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;AAC1C,MAAM,IAAI,CAAC,KAAA,GAAQ,IAAI;AACvB;;AAEA,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,IAAI,CAAC,MAAA,GAAS,KAAK;AACzB;;AAEA,IAAI,IAAI,WAAW,CAAC,MAAM,EAAE;AAC5B,MAAM,IAAI,CAAC,YAAA,GAAe,WAAW;AACrC;;AAEA,IAAI,IAAI,kBAAkB,EAAE;AAC5B,MAAM,IAAI,CAAC,mBAAA,GAAsB,kBAAkB;AACnD;;AAEA,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA,GAAS,KAAK,GAAS;AACvB;AACA,IAAI,IAAI,CAAC,YAAA,GAAe,EAAE;AAC1B,IAAI,IAAI,CAAC,KAAA,GAAQ,EAAE;AACnB,IAAI,IAAI,CAAC,MAAA,GAAS,EAAE;AACpB,IAAI,IAAI,CAAC,KAAA,GAAQ,EAAE;AACnB,IAAI,IAAI,CAAC,SAAA,GAAY,EAAE;AACvB,IAAI,IAAI,CAAC,MAAA,GAAS,SAAS;AAC3B,IAAI,IAAI,CAAC,gBAAA,GAAmB,SAAS;AACrC,IAAI,IAAI,CAAC,YAAA,GAAe,SAAS;AACjC,IAAI,IAAI,CAAC,QAAA,GAAW,SAAS;AAC7B,IAAI,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC;AACrC,IAAI,IAAI,CAAC,YAAA,GAAe,EAAE;AAC1B,IAAI,IAAI,CAAC,qBAAqB,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,MAAM,EAAC,EAAG,CAAC;;AAEzF,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA,GAAS,aAAa,CAAC,UAAU,EAAc,cAAc,EAAiB;AAC9E,IAAI,MAAM,SAAA,GAAY,OAAO,cAAA,KAAmB,QAAA,GAAW,cAAA,GAAiB,uBAAuB;;AAEnG;AACA,IAAI,IAAI,SAAA,IAAa,CAAC,EAAE;AACxB,MAAM,OAAO,IAAI;AACjB;;AAEA,IAAI,MAAM,gBAAgB,GAAe;AACzC,MAAM,SAAS,EAAE,sBAAsB,EAAE;AACzC,MAAM,GAAG,UAAU;AACnB;AACA,MAAM,OAAO,EAAE,UAAU,CAAC,OAAA,GAAU,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,IAAI,UAAU,CAAC,OAAO;AAC3F,KAAK;;AAEL,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAC5C,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAA,GAAS,SAAS,EAAE;AAC9C,MAAM,IAAI,CAAC,YAAA,GAAe,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;AAC7D,MAAM,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,UAAU,CAAC;AACrE;;AAEA,IAAI,IAAI,CAAC,qBAAqB,EAAE;;AAEhC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,iBAAiB,GAA2B;AACrD,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAA,GAAS,CAAC,CAAC;AAC1D;;AAEA;AACA;AACA;AACA,GAAS,gBAAgB,GAAS;AAClC,IAAI,IAAI,CAAC,YAAA,GAAe,EAAE;AAC1B,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,aAAa,CAAC,UAAU,EAAoB;AACrD,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,gBAAgB,GAAS;AAClC,IAAI,IAAI,CAAC,YAAA,GAAe,EAAE;AAC1B,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,YAAY,GAAc;AACnC,IAAI,OAAO;AACX,MAAM,WAAW,EAAE,IAAI,CAAC,YAAY;AACpC,MAAM,WAAW,EAAE,IAAI,CAAC,YAAY;AACpC,MAAM,QAAQ,EAAE,IAAI,CAAC,SAAS;AAC9B,MAAM,IAAI,EAAE,IAAI,CAAC,KAAK;AACtB,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM;AACxB,MAAM,IAAI,EAAE,IAAI,CAAC,KAAK;AACtB,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM;AACxB,MAAM,WAAW,EAAE,IAAI,CAAC,YAAA,IAAgB,EAAE;AAC1C,MAAM,eAAe,EAAE,IAAI,CAAC,gBAAgB;AAC5C,MAAM,kBAAkB,EAAE,IAAI,CAAC,mBAAmB;AAClD,MAAM,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;AACxD,MAAM,eAAe,EAAE,IAAI,CAAC,gBAAgB;AAC5C,MAAM,IAAI,EAAE,gBAAgB,CAAC,IAAI,CAAC;AAClC,KAAK;AACL;;AAEA;AACA;AACA;AACA,GAAS,wBAAwB,CAAC,OAAO,EAA+B;AACxE,IAAI,IAAI,CAAC,sBAAA,GAAyB,KAAK,CAAC,IAAI,CAAC,sBAAsB,EAAE,OAAO,EAAE,CAAC,CAAC;AAChF,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,qBAAqB,CAAC,OAAO,EAA4B;AAClE,IAAI,IAAI,CAAC,mBAAA,GAAsB,OAAO;AACtC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,qBAAqB,GAAuB;AACrD,IAAI,OAAO,IAAI,CAAC,mBAAmB;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,SAAS,EAAW,IAAI,EAAsB;AACxE,IAAI,MAAM,UAAU,IAAI,EAAE,QAAA,IAAY,KAAK,EAAE;;AAE7C,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,eAAe,KAAK,CAAC,IAAI,CAAC,6DAA6D,CAAC;AAC9F,MAAM,OAAO,OAAO;AACpB;;AAEA,IAAI,MAAM,kBAAA,GAAqB,IAAI,KAAK,CAAC,2BAA2B,CAAC;;AAErE,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB;AACjC,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,iBAAiB,EAAE,SAAS;AACpC,QAAQ,kBAAkB;AAC1B,QAAQ,GAAG,IAAI;AACf,QAAQ,QAAQ,EAAE,OAAO;AACzB,OAAO;AACP,MAAM,IAAI;AACV,KAAK;;AAEL,IAAI,OAAO,OAAO;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAC,OAAO,EAAU,KAAK,EAAkB,IAAI,EAAsB;AAC1F,IAAI,MAAM,UAAU,IAAI,EAAE,QAAA,IAAY,KAAK,EAAE;;AAE7C,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,eAAe,KAAK,CAAC,IAAI,CAAC,2DAA2D,CAAC;AAC5F,MAAM,OAAO,OAAO;AACpB;;AAEA,IAAI,MAAM,kBAAA,GAAqB,IAAI,KAAK,CAAC,OAAO,CAAC;;AAEjD,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc;AAC/B,MAAM,OAAO;AACb,MAAM,KAAK;AACX,MAAM;AACN,QAAQ,iBAAiB,EAAE,OAAO;AAClC,QAAQ,kBAAkB;AAC1B,QAAQ,GAAG,IAAI;AACf,QAAQ,QAAQ,EAAE,OAAO;AACzB,OAAO;AACP,MAAM,IAAI;AACV,KAAK;;AAEL,IAAI,OAAO,OAAO;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAsB;AAC9D,IAAI,MAAM,UAAU,IAAI,EAAE,QAAA,IAAY,KAAK,EAAE;;AAE7C,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,eAAe,KAAK,CAAC,IAAI,CAAC,yDAAyD,CAAC;AAC1F,MAAM,OAAO,OAAO;AACpB;;AAEA,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC;;AAE1E,IAAI,OAAO,OAAO;AAClB;;AAEA;AACA;AACA;AACA,GAAY,qBAAqB,GAAS;AAC1C;AACA;AACA;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AACnC,MAAM,IAAI,CAAC,mBAAA,GAAsB,IAAI;AACrC,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY;AAC/C,QAAQ,QAAQ,CAAC,IAAI,CAAC;AACtB,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,mBAAA,GAAsB,KAAK;AACtC;AACA;AACA;;;;"}