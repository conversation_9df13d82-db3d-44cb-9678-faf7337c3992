{"version": 3, "file": "eventProcessors.js", "sources": ["../../src/eventProcessors.ts"], "sourcesContent": ["import { DEBUG_BUILD } from './debug-build';\nimport type { Event, EventHint } from './types-hoist/event';\nimport type { EventProcessor } from './types-hoist/eventprocessor';\nimport { isThenable } from './utils/is';\nimport { debug } from './utils/logger';\nimport { SyncPromise } from './utils/syncpromise';\n\n/**\n * Process an array of event processors, returning the processed event (or `null` if the event was dropped).\n */\nexport function notifyEventProcessors(\n  processors: EventProcessor[],\n  event: Event | null,\n  hint: EventHint,\n  index: number = 0,\n): PromiseLike<Event | null> {\n  return new SyncPromise<Event | null>((resolve, reject) => {\n    const processor = processors[index];\n    if (event === null || typeof processor !== 'function') {\n      resolve(event);\n    } else {\n      const result = processor({ ...event }, hint) as Event | null;\n\n      DEBUG_BUILD && processor.id && result === null && debug.log(`Event processor \"${processor.id}\" dropped event`);\n\n      if (isThenable(result)) {\n        void result\n          .then(final => notifyEventProcessors(processors, final, hint, index + 1).then(resolve))\n          .then(null, reject);\n      } else {\n        void notifyEventProcessors(processors, result, hint, index + 1)\n          .then(resolve)\n          .then(null, reject);\n      }\n    }\n  });\n}\n"], "names": [], "mappings": ";;;;;AAOA;AACA;AACA;AACO,SAAS,qBAAqB;AACrC,EAAE,UAAU;AACZ,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,KAAK,GAAW,CAAC;AACnB,EAA6B;AAC7B,EAAE,OAAO,IAAI,WAAW,CAAe,CAAC,OAAO,EAAE,MAAM,KAAK;AAC5D,IAAI,MAAM,SAAA,GAAY,UAAU,CAAC,KAAK,CAAC;AACvC,IAAI,IAAI,KAAA,KAAU,IAAA,IAAQ,OAAO,SAAA,KAAc,UAAU,EAAE;AAC3D,MAAM,OAAO,CAAC,KAAK,CAAC;AACpB,WAAW;AACX,MAAM,MAAM,MAAA,GAAS,SAAS,CAAC,EAAE,GAAG,KAAA,EAAO,EAAE,IAAI,CAAA;;AAEjD,MAAM,WAAA,IAAe,SAAS,CAAC,MAAM,MAAA,KAAW,IAAA,IAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC;;AAEpH,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE;AAC9B,QAAQ,KAAK;AACb,WAAW,IAAI,CAAC,KAAA,IAAS,qBAAqB,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AAChG,WAAW,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;AAC7B,aAAa;AACb,QAAQ,KAAK,qBAAqB,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,KAAA,GAAQ,CAAC;AACtE,WAAW,IAAI,CAAC,OAAO;AACvB,WAAW,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;AAC7B;AACA;AACA,GAAG,CAAC;AACJ;;;;"}