import { SerializedCheckIn } from './types-hoist/checkin';
import { DsnComponents } from './types-hoist/dsn';
import { CheckInEnvelope, DynamicSamplingContext } from './types-hoist/envelope';
import { SdkMetadata } from './types-hoist/sdkmetadata';
/**
 * Create envelope from check in item.
 */
export declare function createCheckInEnvelope(checkIn: SerializedCheckIn, dynamicSamplingContext?: Partial<DynamicSamplingContext>, metadata?: SdkMetadata, tunnel?: string, dsn?: DsnComponents): CheckInEnvelope;
//# sourceMappingURL=checkin.d.ts.map
