{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MSEt6Fn+4PW7ZQ7o6W4wKDW2ujle0E+X1JIku2TgYjk=", "__NEXT_PREVIEW_MODE_ID": "264e0b3b14e7ded24e40cd942f97b7fa", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3eb3f0673307667ecc37c1f567290a26828680af2ad3122d242c87047cc896fc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5b7d56016b438a9c1b3a167f6198264e82cdfedff505cbcdeb11d1980afceafd"}}}, "functions": {}, "sortedMiddleware": ["/"]}