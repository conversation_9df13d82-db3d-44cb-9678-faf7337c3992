import type { LocalVariablesIntegrationOptions } from './common';
export declare const base64WorkerScript = "###LocalVariablesWorkerScript###";
/**
 * Adds local variables to exception frames
 */
export declare const localVariablesAsyncIntegration: (integrationOptions?: LocalVariablesIntegrationOptions | undefined) => import("@sentry/core").Integration;
//# sourceMappingURL=local-variables-async.d.ts.map