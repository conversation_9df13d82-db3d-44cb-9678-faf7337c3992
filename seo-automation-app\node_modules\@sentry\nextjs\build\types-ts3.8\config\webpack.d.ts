import { RouteManifest } from './manifest/types';
import { NextConfigObject, SentryBuildOptions, WebpackConfigFunction } from './types';
/**
 * Construct the function which will be used as the nextjs config's `webpack` value.
 *
 * Sets:
 *   - `devtool`, to ensure high-quality sourcemaps are generated
 *   - `entry`, to include user's sentry config files (where `Sentry.init` is called) in the build
 *   - `plugins`, to add SentryWebpackPlugin
 *
 * @param userNextConfig The user's existing nextjs config, as passed to `withSentryConfig`
 * @param userSentryOptions The user's SentryWebpackPlugin config, as passed to `withSentryConfig`
 * @returns The function to set as the nextjs config's `webpack` value
 */
export declare function constructWebpackConfigFunction(userNextConfig: NextConfigObject | undefined, userSentryOptions: SentryBuildOptions | undefined, releaseName: string | undefined, routeManifest: RouteManifest | undefined): WebpackConfigFunction;
//# sourceMappingURL=webpack.d.ts.map
