/**
 * If this attribute is attached to a transaction, the Next.js SDK will drop that transaction.
 */
export declare const TRANSACTION_ATTR_SHOULD_DROP_TRANSACTION = "sentry.drop_transaction";
export declare const TRANSACTION_ATTR_SENTRY_TRACE_BACKFILL = "sentry.sentry_trace_backfill";
export declare const TRANSACTION_ATTR_SENTRY_ROUTE_BACKFILL = "sentry.route_backfill";
//# sourceMappingURL=span-attributes-with-logic-attached.d.ts.map
