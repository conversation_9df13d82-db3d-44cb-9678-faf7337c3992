import { <PERSON>lientReport } from '../types-hoist/clientreport';
import { ClientReportEnvelope } from '../types-hoist/envelope';
/**
 * Creates client report envelope
 * @param discarded_events An array of discard events
 * @param dsn A DSN that can be set on the header. Optional.
 */
export declare function createClientReportEnvelope(discarded_events: ClientReport['discarded_events'], dsn?: string, timestamp?: number): ClientReportEnvelope;
//# sourceMappingURL=clientreport.d.ts.map
