import { ConsoleLevel } from '../types-hoist/instrument';
/**
 * An error emitted by Sentry SDKs and related utilities.
 * @deprecated This class is no longer used and will be removed in a future version. Use `Error` instead.
 */
export declare class SentryError extends Error {
    message: string;
    logLevel: ConsoleLevel;
    constructor(message: string, logLevel?: ConsoleLevel);
}
//# sourceMappingURL=error.d.ts.map
