import type { SentryWebpackPluginOptions } from '@sentry/webpack-plugin';
import type { BuildContext, SentryBuildOptions } from './types';
/**
 * Combine default and user-provided SentryWebpackPlugin options, accounting for whether we're building server files or
 * client files.
 */
export declare function getWebpackPluginOptions(buildContext: BuildContext, sentryBuildOptions: SentryBuildOptions, releaseName: string | undefined): SentryWebpackPluginOptions;
//# sourceMappingURL=webpackPluginOptions.d.ts.map