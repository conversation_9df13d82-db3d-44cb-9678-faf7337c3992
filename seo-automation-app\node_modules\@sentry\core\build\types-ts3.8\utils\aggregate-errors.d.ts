import { Event, EventHint } from '../types-hoist/event';
import { Exception } from '../types-hoist/exception';
import { StackParser } from '../types-hoist/stacktrace';
/**
 * Creates exceptions inside `event.exception.values` for errors that are nested on properties based on the `key` parameter.
 */
export declare function applyAggregateErrorsToEvent(exceptionFromErrorImplementation: (stackParser: StackParser, ex: Error) => Exception, parser: StackParser, key: string, limit: number, event: Event, hint?: EventHint): void;
//# sourceMappingURL=aggregate-errors.d.ts.map
